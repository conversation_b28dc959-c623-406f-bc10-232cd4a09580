<template>
  <div class="login-container">
    <div class="login-left-box">
      <img src="../assets/copywriter.png" alt="Spacetime&AIOS" class="main-logo" />
    </div>
    <div class="login-right-box">
      <img src="../assets/logo.svg" alt="logo" class="logo" />
      <div class="welcome-text">
        <span class="hello-text">Hello!</span>
        <span class="welcome-title">欢迎注册热爱元宇宙</span>
      </div>

      <el-form :model="registerForm" class="login-form">
        <!-- 住和角色 同一行 并且平分一行 -->
        <el-form-item>
          <div class="form-row">
            <el-select
              v-model="registerForm.residence"
              placeholder="住"
              class="form-input form-half"
              size="large"
            >
              <el-option label="选项1" value="option1" />
              <el-option label="选项2" value="option2" />
            </el-select>
            <el-select
              v-model="registerForm.role"
              placeholder="角色"
              class="form-input form-half house-role-form"
              size="large"
            >
              <el-option label="角色1" value="role1" />
              <el-option label="角色2" value="role2" />
            </el-select>
          </div>
        </el-form-item>

        <!-- 手机号输入框 -->
        <el-form-item>
          <el-input
            v-model="registerForm.phone"
            placeholder="手机号"
            class="form-input"
            size="large"
          />
        </el-form-item>

        <!-- 密码输入框 -->
        <el-form-item>
          <el-input
            v-model="registerForm.password"
            type="password"
            placeholder="密码"
            class="form-input"
            size="large"
            show-password
          />
        </el-form-item>

        <!-- 确认密码输入框 -->
        <el-form-item>
          <el-input
            v-model="registerForm.confirmPassword"
            type="password"
            placeholder="确认密码"
            class="form-input"
            size="large"
            show-password
          />
        </el-form-item>

        <!-- 验证码输入框 -->
        <el-form-item>
          <el-input
            v-model="registerForm.verifyCode"
            placeholder="验证码"
            class="form-input verify-code-input"
            size="large"
          >
            <template #suffix>
              <el-button
                type="text"
                class="send-code-btn"
                @click="sendVerifyCode"
              >
                发送验证码
              </el-button>
            </template>
          </el-input>
        </el-form-item>

        <!-- 邀请码输入框 -->
        <el-form-item>
          <el-input
            v-model="registerForm.inviteCode"
            placeholder="邀请码"
            class="form-input"
            size="large"
          />
        </el-form-item>

        <!-- 下一步按钮 -->
        <el-form-item>
          <el-button
            type="primary"
            class="login-btn"
            size="large"
            @click="handleRegister"
          >
            下一步
          </el-button>
        </el-form-item>

        <!-- 底部链接 -->
        <el-form-item>
          <div class="footer-links">
            <div class="agreement-text">
              <el-checkbox v-model="registerForm.agreeTerms" class="agreement-checkbox">
                我已阅读并同意<span class="agreement-link">《用户注册协议》</span>
              </el-checkbox>
            </div>
            <span class="link-text">已有账号？<span class="register-link" @click="goToLogin">去登录</span></span>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()

// 注册表单数据
const registerForm = ref({
  residence: '',
  role: '',
  phone: '',
  password: '',
  confirmPassword: '',
  verifyCode: '',
  inviteCode: '',
  agreeTerms: false
})

// 发送验证码
const sendVerifyCode = () => {
  if (!registerForm.value.phone) {
    ElMessage.warning('请输入手机号')
    return
  }
  // 这里添加发送验证码的逻辑
  ElMessage.success('验证码已发送')
}

// 处理注册
const handleRegister = () => {
  // 表单验证
  if (!registerForm.value.residence) {
    ElMessage.warning('请选择住址')
    return
  }
  if (!registerForm.value.role) {
    ElMessage.warning('请选择角色')
    return
  }
  if (!registerForm.value.phone) {
    ElMessage.warning('请输入手机号')
    return
  }
  if (!registerForm.value.password) {
    ElMessage.warning('请输入密码')
    return
  }
  if (!registerForm.value.confirmPassword) {
    ElMessage.warning('请确认密码')
    return
  }
  if (registerForm.value.password !== registerForm.value.confirmPassword) {
    ElMessage.warning('两次密码输入不一致')
    return
  }
  if (!registerForm.value.verifyCode) {
    ElMessage.warning('请输入验证码')
    return
  }
  if (!registerForm.value.agreeTerms) {
    ElMessage.warning('请阅读并同意用户注册协议')
    return
  }

  // 这里添加注册逻辑
  ElMessage.success('注册成功')
  router.push('/login')
}

// 跳转到登录页面
const goToLogin = () => {
  router.push('/login')
}
</script>

<style scoped>
.login-container {
  margin: 10px;
  width: calc(100% - 20px);
  height: calc(100% - 20px);
  display: flex;
  flex-direction: row;
}

.login-left-box {
  background-image: url('../assets/banner.png');
  width: 1184px;
  height: 940px;
  background-size: cover;
  background-position: center;
  border-radius: 20px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
}

.main-logo {
  width: 722px;
  height: 338px;
}

.login-right-box {
  display: flex;
  width: 706px;
  padding: 40px;
  flex-direction: column;
  flex-shrink: 0;
  align-self: stretch;
  border-radius: 20px;
  background: #ffffff;
}

.logo {
  height: 45px;
  width: auto;
  align-self: flex-start;
  margin-bottom: 60px;
}

.welcome-text {
  display: flex;
  flex-direction: column;
  margin-bottom: 40px;
}

.hello-text {
  color: var(--text-1, #1D2129);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 40px;
  font-style: normal;
  font-weight: 900;
  line-height: normal;
}

.welcome-title {
  color: var(--text-1, #1D2129);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 40px;
  font-style: normal;
  font-weight: 900;
  line-height: normal;
}

.login-form {
  width: 100%;
}

.form-input {
  width: 100%;
  margin-bottom: 12px;
}

/* 表单行样式 */
.form-row {
  display: flex;
  gap: 10px;
  align-items: center;
}

/* 住和角色单独的样式 */
.house-role-form {
  
}

.form-half {
  flex: 1;
  margin-bottom: 0;
}

:deep(.form-input .el-input__wrapper) {
  background-color: #f2f3f5;
  border: 1px solid #E5E6EB;
  border-radius: 10px;
  padding: 0px 16px;
  height: 56px;
  box-shadow: none;
}

:deep(.form-input .el-input__wrapper.is-focus) {
  border-color: #2F7DFB;
  box-shadow: 0 0 0 2px rgba(47, 125, 251, 0.1);
}

:deep(.form-input .el-input__inner) {
  background: transparent;
  border: none;
  font-size: 16px;
  color: #1D2129;
}

:deep(.form-input .el-input__inner::placeholder) {
  color: #86909C;
}

/* 下拉框样式 */
:deep(.form-input.el-select .el-select__wrapper) {
  background-color: #f2f3f5;
  border: 1px solid #E5E6EB;
  border-radius: 10px;
  padding: 0px 16px;
  height: 56px;
  box-shadow: none;
}

:deep(.form-input.el-select .el-select__wrapper.is-focused) {
  border-color: #2F7DFB;
  box-shadow: 0 0 0 2px rgba(47, 125, 251, 0.1);
}

:deep(.form-input.el-select .el-select__placeholder) {
  color: #86909C;
  font-size: 16px;
}

:deep(.form-input.el-select .el-select__selected-item) {
  color: #1D2129;
  font-size: 16px;
}

:deep(.form-input.el-select .el-select__caret) {
  color: #86909C;
}

/* 验证码输入框样式 */
.verify-code-input {
  position: relative;
}

:deep(.verify-code-input .el-input__suffix) {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  display: flex;
  align-items: center;
  padding-right: 16px;
}

.send-code-btn {
  color: #2F7DFB;
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  padding: 0;
  height: auto;
  border: none;
  background: transparent;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.send-code-btn:hover {
  opacity: 0.8;
}

:deep(.send-code-btn.is-disabled) {
  color: #86909C;
  cursor: not-allowed;
}



.login-btn {
  width: 100%;
  height: 60px;
  border-radius: 10px;
  font-size: 18px;
  font-family: "Alibaba PuHuiTi 2.0";
  border: 1px solid rgba(255, 255, 255, 0.20);
  background: linear-gradient(180deg, #2F7DFB 0%, #125CD2 100%), linear-gradient(0deg, #2F7DFB 0%, #2F7DFB 100%), linear-gradient(180deg, #2F7DFB 0%, #125CD2 100%);
  color: #FFF;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 1.8px;
}

.footer-links {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
  width: 100%;
}

.agreement-text {
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.agreement-checkbox .el-checkbox__label) {
  color: var(--text-3, #86909C);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.agreement-link {
  color: var(--primary, #2F7DFB);
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.agreement-link:hover {
  opacity: 0.8;
}

.link-text {
  color: var(--text-3, #86909C);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  cursor: pointer;
  transition: color 0.3s ease;
}

.register-link {
  color: var(--primary, #2F7DFB);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  cursor: pointer;
  transition: color 0.3s ease;
}

.register-link:hover {
  opacity: 0.8;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
    margin: 0;
    width: 100%;
    height: 100vh;
  }

  .login-left-box {
    width: 100%;
    height: 40vh;
    min-height: 300px;
    border-radius: 0 0 20px 20px;
  }

  .main-logo {
    width: 60vw;
    height: auto;
    max-width: 400px;
  }

  .login-right-box {
    width: 100%;
    height: 60vh;
    padding: 30px 20px;
    border-radius: 20px 20px 0 0;
    margin-top: -20px;
    z-index: 10;
    position: relative;
  }

  .logo {
    height: 35px;
    margin-bottom: 30px;
  }

  .hello-text,
  .welcome-title {
    font-size: 28px;
  }

  .welcome-text {
    margin-bottom: 30px;
  }

  :deep(.form-input .el-input__wrapper) {
    height: 50px;
    padding: 10px 14px;
  }

  .login-btn {
    height: 50px;
    font-size: 16px;
  }

  .form-input {
    width: 100%;
  }

  .footer-links {
    flex-direction: column;
    gap: 15px;
    margin-top: 15px;
    margin-bottom: 50px;
  }

  .link-text {
    font-size: 13px;
  }

  .form-row {
    flex-direction: column;
    gap: 15px;
  }

  .form-half {
    width: 100%;
    flex: none;
  }


}

/* 小屏幕手机适配 */
@media (max-width: 480px) {
  .login-right-box {
    padding: 20px 16px;
  }

  .hello-text,
  .welcome-title {
    font-size: 24px;
  }

  .welcome-text {
    margin-bottom: 25px;
  }

  :deep(.form-input .el-input__wrapper) {
    height: 48px;
    padding: 8px 12px;
  }

  :deep(.form-input .el-input__inner) {
    font-size: 15px;
  }

  .login-btn {
    height: 48px;
    font-size: 15px;
  }

  .form-input {
    width: 100%;
  }

  .form-row {
    flex-direction: column;
    gap: 15px;
  }

  .form-half {
    width: 100%;
    flex: none;
  }

  .footer-links {
    flex-direction: column;
    gap: 15px;
  }
}

@font-face {
  font-family: "江城斜黑体";
  src: url("../assets/typeface/JiangXieHei500W.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: "江城斜黑体";
  src: url("../assets/typeface/JiangXieHei900W.ttf") format("truetype");
  font-weight: 900;
  font-style: normal;
}
</style>